package com.zjhh.sat.jxsrcxfx.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zjhh.comm.response.ReData;
import com.zjhh.cz.req.IdStringReq;
import com.zjhh.db.comm.Page;
import com.zjhh.sat.jxsrcxfx.request.streetentcheck.*;
import com.zjhh.sat.jxsrcxfx.service.StreetEntCheckService;
import com.zjhh.sat.jxsrcxfx.vo.streetentcheck.BatchStreetEntVo;
import com.zjhh.sat.jxsrcxfx.vo.streetentcheck.StreetCheckIssuedVo;
import com.zjhh.sat.jxsrcxfx.vo.streetentcheck.StreetEntCheckVo;
import com.zjhh.sat.jxsrcxfx.vo.streetentcheck.StreetStatusVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/16 15:29
 */
@Tag(name = "嘉兴体制结算-街道企业核定")
@SaCheckLogin
@RequestMapping("street/ent/check")
@RestController
public class StreetEntCheckController extends BaseJxsrcxfxController {

    @Resource
    private StreetEntCheckService streetEntCheckService;

    @Operation(summary = "街道企业核定批次-列表")
    @SaCheckPermission("streetEntCheck")
    @PostMapping("page")
    public ReData<Page<StreetEntCheckVo>> pageStreetEntCheck(@RequestBody @Validated PageStreetEntCheckReq req) {
        return ReData.success(streetEntCheckService.pageStreetEntCheck(req));
    }

    @Operation(summary = "企业核定下发表-列表")
    @SaCheckPermission("streetEntCheck")
    @PostMapping("page/check")
    public ReData<Page<StreetCheckIssuedVo>> pageStreetCheckIssued(@RequestBody @Validated PageStreetCheckIssuedReq req) {
        return ReData.success(streetEntCheckService.pageStreetCheckIssued(req));
    }

    @Operation(summary = "财政分片")
    @SaCheckPermission("streetEntCheck")
    @PostMapping("update/czfp")
    public ReData<String> updateCzfp(@RequestBody @Validated UpdateCzfpReq req) {
        streetEntCheckService.updateCzfp(req);
        return ReData.success();
    }

    @Operation(summary = "撤销分片")
    @SaCheckPermission("streetEntCheck")
    @PostMapping("update/revoke/czfp")
    public ReData<String> updateRevokeCzfp(@RequestBody @Validated UpdateCzfpReq req) {
        streetEntCheckService.updateRevokeCzfp(req);
        return ReData.success();
    }

    @Operation(summary = "确认上报")
    @SaCheckPermission("streetEntCheck")
    @PostMapping("update/reported")
    public ReData<String> UpdateReported(@RequestBody @Validated IdStringReq req) {
        streetEntCheckService.UpdateReported(req);
        return ReData.success();
    }

    @Operation(summary = "撤销上报")
    @SaCheckPermission("streetEntCheck")
    @PostMapping("update/revoke/reported")
    public ReData<String> updateRevokeReported(@RequestBody @Validated IdStringReq req) {
        streetEntCheckService.updateRevokeReported(req);
        return ReData.success();
    }

    @Operation(summary = "街道企业核定批次-列表导出")
    @SaCheckPermission("streetEntCheck:export")
    @PostMapping("export/street/ent/check")
    public void exportStreetEntCheck(@RequestBody @Validated ListBatchStreetEntReq req, HttpServletResponse response) throws IOException {
        commonStaticExport(response, streetEntCheckService.listBatchStreetEnt(req), BatchStreetEntVo.class, "街道企业核定批次", null);
    }

    @Operation(summary = "企业核定下发表-列表导出")
    @SaCheckPermission("streetEntCheck:export")
    @PostMapping("export/street/check/issued")
    public void pageStreetCheckIssued(@RequestBody @Validated PageStreetCheckIssuedReq req, HttpServletResponse response) throws IOException {
        Page<StreetCheckIssuedVo> page = streetEntCheckService.pageStreetCheckIssued(req);
        List<StreetCheckIssuedVo> list = page.getRecords();
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setXh(i + 1);
        }
        commonStaticExport(response, list, StreetCheckIssuedVo.class, "企业核定下发表", null);
    }

    @Operation(summary = "获取企业核定下发表批次状态")
    @SaCheckPermission("streetEntCheck")
    @PostMapping("get/street/status")
    public ReData<StreetStatusVo> getStreetStatus(@RequestBody @Validated IdStringReq req) {
        return ReData.success(streetEntCheckService.getStreetStatus(req));
    }
}
