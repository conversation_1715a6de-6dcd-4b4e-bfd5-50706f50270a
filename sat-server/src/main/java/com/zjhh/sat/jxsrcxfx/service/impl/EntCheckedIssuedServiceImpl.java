package com.zjhh.sat.jxsrcxfx.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.zjhh.comm.exception.BizException;
import com.zjhh.db.comm.Page;
import com.zjhh.sat.jxsrcxfx.constants.JxTzjsConstant;
import com.zjhh.sat.jxsrcxfx.dao.entity.*;
import com.zjhh.sat.jxsrcxfx.dao.mapper.*;
import com.zjhh.sat.jxsrcxfx.request.entcheckedissued.PageEntCheckedIssuedReq;
import com.zjhh.sat.jxsrcxfx.request.entcheckedissued.UpdateCzfqReq;
import com.zjhh.sat.jxsrcxfx.request.entcheckedissued.UpdateIssuedReq;
import com.zjhh.sat.jxsrcxfx.request.entcheckedissued.UpdateWljzReq;
import com.zjhh.sat.jxsrcxfx.service.EntCheckedIssuedService;
import com.zjhh.sat.jxsrcxfx.utils.BatchNoUtil;
import com.zjhh.sat.jxsrcxfx.vo.entcheckedissued.EntCheckedIssuedVo;
import com.zjhh.sat.tzjs.dao.entity.AdsTzjsPublicChange;
import com.zjhh.sat.tzjs.dao.entity.AdsTzjsPublicNsrxx;
import com.zjhh.sat.tzjs.dao.mapper.AdsTzjsPublicChangeMapper;
import com.zjhh.sat.tzjs.dao.mapper.AdsTzjsPublicHdjlMapper;
import com.zjhh.sat.tzjs.dao.mapper.AdsTzjsPublicNsrxxMapper;
import com.zjhh.user.service.impl.UserSession;
import com.zjhh.user.vo.LoginVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/23 10:42
 */
@Slf4j
@Service
@DS("tzjs")
public class EntCheckedIssuedServiceImpl implements EntCheckedIssuedService {

    @Resource
    private AdsSatBddjNsrxxMapper adsSatBddjNsrxxMapper;

    @Resource
    private AdsSatWljzNsrxxMapper adsSatWljzNsrxxMapper;

    @Resource
    private AdsSatIssuedBatchMapper adsSatIssuedBatchMapper;

    @Resource
    private AdsSatIssuedNsrxxMapper adsSatIssuedNsrxxMapper;

    @Resource
    private AdsTzjsPublicNsrxxMapper adsTzjsPublicNsrxxMapper;

    @Resource
    private AdsTzjsPublicHdjlMapper adsTzjsPublicHdjlMapper;

    @Resource
    private AdsTzjsPublicChangeMapper adsTzjsPublicChangeMapper;

    @Resource
    private VAdsTzjsPublicNsrxxMapper vAdsTzjsPublicNsrxxMapper;

    @Resource
    private UserSession userSession;

    @Override
    public Page<EntCheckedIssuedVo> pageEntCheckedIssued(PageEntCheckedIssuedReq req) {
        Page<EntCheckedIssuedVo> page = req.getPage(EntCheckedIssuedVo.class);
        List<EntCheckedIssuedVo> list = adsSatBddjNsrxxMapper.listEntCheckedIssued(req);
        page.setRecords(list);
        if (CollUtil.isEmpty(list)) {
            page.setTotal(list.getFirst().getTotal());
        }
        return page;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateWljz(UpdateWljzReq req) {
        QueryWrapper<AdsSatBddjNsrxx> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(AdsSatBddjNsrxx::getQyid, req.getQyids())
                .eq(AdsSatBddjNsrxx::getYswljz, "N");
        if (adsSatBddjNsrxxMapper.selectCount(wrapper) > 0) {
            throw new BizException("只有疑似外来建筑的企业才能设置为外来建筑！");
        }
        List<AdsSatWljzNsrxx> list = new ArrayList<>(req.getQyids().size());
        String userCode = userSession.getUserCode();
        LocalDateTime now = LocalDateTime.now();
        req.getQyids().forEach(qyid -> {
            AdsSatWljzNsrxx adsSatWljzNsrxx = new AdsSatWljzNsrxx();
            adsSatWljzNsrxx.setQyid(qyid);
            adsSatWljzNsrxx.setCreateTime(now);
            adsSatWljzNsrxx.setCreateUser(userCode);
            list.add(adsSatWljzNsrxx);
        });
        if (CollUtil.isNotEmpty(list)) {
            adsSatWljzNsrxxMapper.insertBatchSomeColumn(list);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateCzfp(UpdateCzfqReq req) {
        LoginVo loginVo = userSession.getSessionLoginVo();
        String orgCode = loginVo.getDefaultAreaCode();
        String userCode = loginVo.getCode();
        if (req.getCheckStatus() == 1) {
            QueryWrapper<AdsTzjsPublicNsrxx> wrapper = new QueryWrapper<>();
            wrapper.lambda().in(AdsTzjsPublicNsrxx::getQyid, req.getQyids())
                    .eq(AdsTzjsPublicNsrxx::getDataOwner, JxTzjsConstant.DATA_OWNER);
            if (adsTzjsPublicNsrxxMapper.selectCount(wrapper) > 0) {
                throw new BizException("当前企业存在已核定企业，请刷新页面后再操作！");
            }
            UpdateWrapper<AdsTzjsPublicNsrxx> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(AdsTzjsPublicNsrxx::getQyid, req.getQyids())
                    .eq(AdsTzjsPublicNsrxx::getDataOwner, JxTzjsConstant.DATA_OWNER)
                    .set(AdsTzjsPublicNsrxx::getXzqhDm, req.getXzqhDm())
                    .set(AdsTzjsPublicNsrxx::getJdxzDm, req.getJdxzDm())
                    .set(AdsTzjsPublicNsrxx::getHdFlag, 1);
            adsTzjsPublicNsrxxMapper.update(updateWrapper);

            String qyids = String.join(",", req.getQyids());
            adsTzjsPublicHdjlMapper.callHdjlDeal(qyids, null, null, req.getJdxzDm(), orgCode, userCode);
        } else if (req.getCheckStatus() == 2) {
            checkAdjust(req.getQyids());

            UpdateWrapper<AdsTzjsPublicNsrxx> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(AdsTzjsPublicNsrxx::getQyid, req.getQyids())
                    .eq(AdsTzjsPublicNsrxx::getDataOwner, JxTzjsConstant.DATA_OWNER)
                    .set(AdsTzjsPublicNsrxx::getXzqhDm, req.getXzqhDm())
                    .set(AdsTzjsPublicNsrxx::getJdxzDm, req.getJdxzDm());
            adsTzjsPublicNsrxxMapper.update(updateWrapper);

            QueryWrapper<AdsTzjsPublicNsrxx> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(AdsTzjsPublicNsrxx::getQyid, req.getQyids())
                    .eq(AdsTzjsPublicNsrxx::getDataOwner, JxTzjsConstant.DATA_OWNER);
            List<AdsTzjsPublicNsrxx> list = adsTzjsPublicNsrxxMapper.selectList(wrapper);
            if (CollUtil.isNotEmpty(list)) {
                list.forEach(nsrxx -> adsTzjsPublicHdjlMapper.callHdjlDeal(nsrxx.getQyid(), null, nsrxx.getJdxzDm(), req.getJdxzDm(), orgCode, userCode));
            }
        } else {
            throw new BizException("核定状态错误！");
        }

    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateIssued(UpdateIssuedReq req) {
        List<AdsSatIssuedBatch> batches = new ArrayList<>();
        List<AdsSatIssuedNsrxx> nsrxxes = new ArrayList<>();
        String batchNo = BatchNoUtil.crateBatchNo();
        String userCode = userSession.getUserCode();
        LocalDateTime now = LocalDateTime.now();
        req.getXzqhDms().forEach(xzqhDm -> {
            AdsSatIssuedBatch batch = new AdsSatIssuedBatch();
            batch.setId(IdUtil.getSnowflakeNextIdStr());
            batch.setBatchNo(batchNo);
            batch.setParentId("root");
            batch.setAreaCode(xzqhDm);
            batch.setReported(false);
            batch.setConfirmed(false);
            batch.setCreateUser(userCode);
            batch.setCreateTime(now);
            batches.add(batch);
        });
        adsSatIssuedBatchMapper.insertBatchSomeColumn(batches);
        req.getQyids().forEach(qyid -> {
            AdsSatIssuedNsrxx nsrxx = new AdsSatIssuedNsrxx();
            nsrxx.setId(IdUtil.getSnowflakeNextIdStr());
            nsrxx.setQyid(qyid);
            nsrxx.setBatchNo(batchNo);
            // 父批次类型为1
            nsrxx.setBatchType(1);
            nsrxx.setStatus(0);
            nsrxx.setCreateUser(userCode);
            nsrxx.setCreateTime(now);
            nsrxxes.add(nsrxx);
        });
        adsSatIssuedNsrxxMapper.insertBatchSomeColumn(nsrxxes);

        UpdateWrapper<AdsTzjsPublicNsrxx> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(AdsTzjsPublicNsrxx::getQyid, req.getQyids())
                .eq(AdsTzjsPublicNsrxx::getDataOwner, JxTzjsConstant.DATA_OWNER)
                .set(AdsTzjsPublicNsrxx::getHdFlag, 1);
        adsTzjsPublicNsrxxMapper.update(updateWrapper);
    }

    /**
     * 判断是否有在调整的记录
     */
    private void checkAdjust(List<String> qyids) {
        QueryWrapper<AdsTzjsPublicChange> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsTzjsPublicChange::getDataOwner, JxTzjsConstant.DATA_OWNER)
                .in(AdsTzjsPublicChange::getQyid, qyids)
                .in(AdsTzjsPublicChange::getStatus, 0, 1)
                .select(AdsTzjsPublicChange::getQyid);
        List<String> qyidList = adsTzjsPublicChangeMapper.selectStrings(wrapper);
        if (CollUtil.isEmpty(qyidList)) {
            return;
        }
        QueryWrapper<VAdsTzjsPublicNsrxx> nsrxxWrapper = new QueryWrapper<>();
        nsrxxWrapper.lambda().in(VAdsTzjsPublicNsrxx::getQyid, qyids)
                .select(VAdsTzjsPublicNsrxx::getNsrmc);
        List<VAdsTzjsPublicNsrxx> list = vAdsTzjsPublicNsrxxMapper.selectList(nsrxxWrapper);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        StringBuilder sb = new StringBuilder("操作失败！纳税人名称为：");

        if (list.size() <= 3) {
            list.forEach(nsrxx -> sb.append(nsrxx.getNsrmc()).append("、"));
            sb.deleteCharAt(sb.length() - 1);
        } else {
            for (int i = 0; i < 3; i++) {
                sb.append(list.get(i).getNsrmc()).append("、");
            }
            sb.deleteCharAt(sb.length() - 1);
            sb.append("等").append(list.size()).append("项");
        }
        sb.append("正在调整中！");
        throw new BizException(sb.toString());
    }
}
