package com.zjhh.sat.jxsrcxfx.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.sat.jxsrcxfx.request.entcheckedissued.PageEntCheckedIssuedReq;
import com.zjhh.sat.jxsrcxfx.vo.entcheckedissued.EntCheckedIssuedVo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 税务本地登记纳税人信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-23
 */
public interface AdsSatBddjNsrxxMapper extends CustomerBaseMapper<AdsSatBddjNsrxx> {

    /**
     * 获取企业核定下发列表
     *
     * @param req
     * @return
     */
    Page<EntCheckedIssuedVo> pageEntCheckedIssued(Page<EntCheckedIssuedVo> page, @Param("req") PageEntCheckedIssuedReq req);

}
