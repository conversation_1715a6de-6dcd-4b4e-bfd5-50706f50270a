package com.zjhh.sat.jxsrcxfx.vo.entcheckedissued;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/11/23 10:46
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntCheckedIssuedVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -6701651882482703598L;

    @ExcelIgnore
    @Schema(description = "企业id")
    private String qyid;

    @ExcelIgnore
    @Schema(description = "行政区划代码")
    private String xzqhDm;

    @ExcelProperty(value = "行政区划", index = 0)
    @Schema(description = "行政区划名称")
    private String xzqhMc;

    @ExcelIgnore
    @Schema(description = "财政分片代码")
    private String jdxzDm;

    @ExcelProperty(value = "财政分片", index = 1)
    @Schema(description = "财政分片名称")
    private String jdxzMc;

    @ExcelProperty(value = "纳税人识别号", index = 2)
    @Schema(description = "纳税人识别号")
    private String nsrsbh;

    @ExcelProperty(value = "纳税人名称", index = 3)
    @Schema(description = "纳税人名称")
    private String nsrmc;

    @ExcelProperty(value = "行业名称", index = 4)
    @Schema(description = "行业名称")
    private String hyMc;

    @ExcelProperty(value = "登记注册类型", index = 5)
    @Schema(description = "登记注册类型")
    private String djzclxMc;

    @ExcelProperty(value = "生产经营地址", index = 6)
    @Schema(description = "生产经营地址")
    private String scjydz;

    @ExcelProperty(value = "注册地址", index = 7)
    @Schema(description = "注册地址")
    private String zcdz;

    @ExcelProperty(value = "主管税务局", index = 8)
    @Schema(description = "主管税务局")
    private String swjgMc;

    @ExcelProperty(value = "社会信用代码", index = 9)
    @Schema(description = "社会信用代码")
    private String shxydm;

    @Schema(hidden = true)
    private Integer total;
}
