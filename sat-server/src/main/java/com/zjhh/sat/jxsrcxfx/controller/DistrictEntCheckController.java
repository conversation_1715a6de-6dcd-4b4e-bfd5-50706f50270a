package com.zjhh.sat.jxsrcxfx.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import com.zjhh.comm.response.ReData;
import com.zjhh.comm.vo.SingleSelectVo;
import com.zjhh.cz.req.IdStringReq;
import com.zjhh.db.comm.Page;
import com.zjhh.sat.jxsrcxfx.request.districtentcheck.*;
import com.zjhh.sat.jxsrcxfx.service.DistrictEntCheckService;
import com.zjhh.sat.jxsrcxfx.vo.districtentcheck.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/15 15:40
 */
@Tag(name = "嘉兴体制结算-区级企业核定")
@SaCheckLogin
@RequestMapping("district/ent/check")
@RestController
public class DistrictEntCheckController extends BaseJxsrcxfxController {

    @Resource
    private DistrictEntCheckService districtEntCheckService;

    @Operation(summary = "区级企业核定批次-列表")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("page/district/issued/batch")
    public ReData<Page<DistrictIssuedBatchVo>> pageDistrictIssuedBatch(@RequestBody @Validated PageDistrictEntIssuedBatchReq req) {
        return ReData.success(districtEntCheckService.pageDistrictIssuedBatch(req));
    }

    @Operation(summary = "企业核定下发表-下发")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("update/district/issued")
    public ReData<String> updateDistrictIssued(@RequestBody @Validated UpdateDistrictIssuedReq req) {
        districtEntCheckService.updateDistrictIssued(req);
        return ReData.success();
    }

    @Operation(summary = "企业核定下发表-财政分片")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("update/district/czfp")
    public ReData<String> updateDistrictCzfp(@RequestBody @Validated UpdateDistrictCzfpReq req) {
        districtEntCheckService.updateDistrictCzfp(req);
        return ReData.success();
    }

    @Operation(summary = "企业核定下发表-撤销财政分片")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("update/district/revoke/czfp")
    public ReData<String> updateDistrictRevokeCzfp(@RequestBody @Validated updateDistrictRevokeCzfpReq req) {
        districtEntCheckService.updateDistrictRevokeCzfp(req);
        return ReData.success();
    }

    @Operation(summary = "企业核定下发表-确认上报")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("update/district/report")
    public ReData<String> updateDistrictReport(@RequestBody @Validated IdStringReq req) {
        districtEntCheckService.updateDistrictReport(req);
        return ReData.success();
    }

    @Operation(summary = "企业核定下发表-撤销上报")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("update/district/revoke/report")
    public ReData<String> updateDistrictRevokeReport(@RequestBody @Validated IdStringReq req) {
        districtEntCheckService.updateDistrictRevokeReport(req);
        return ReData.success();
    }

    @Operation(summary = "企业核定下发表-列表")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("page/district/issued/check")
    public ReData<Page<DistrictIssuedCheckVo>> pageDistrictIssuedCheck(@RequestBody @Validated PageDistrictIssuedCheckReq req) {
        return ReData.success(districtEntCheckService.pageDistrictIssuedCheck(req));
    }

    @Operation(summary = "街道企业核定情况-撤销下发-街道列表")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("list/revoke/issued/street")
    public ReData<List<SingleSelectVo>> listRevokeIssuedStreet(@RequestBody @Validated BatchNoReq req) {
        return ReData.success(districtEntCheckService.listRevokeIssuedStreet(req));
    }

    @Operation(summary = "街道企业核定情况-撤销下发")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("update/street/revoke/issued")
    public ReData<StreetRevokeIssuedVo> updateStreetRevokeIssued(@RequestBody @Validated UpdateStreetRevokeIssuedReq req) {
        return ReData.success(districtEntCheckService.updateStreetRevokeIssued(req));
    }

    @Operation(summary = "街道企业核定情况-财政分片")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("update/street/czfp")
    public ReData<String> updateStreetCzfp(@RequestBody @Validated UpdateStreetCzfpReq req) {
        districtEntCheckService.updateStreetCzfp(req);
        return ReData.success();
    }

    @Operation(summary = "街道企业核定情况-退回-街道乡镇列表")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("list/street/revoke/jdxz")
    public ReData<List<SingleSelectVo>> listStreetRevokeJdxz(@RequestBody @Validated BatchNoReq req) {
        return ReData.success(districtEntCheckService.listStreetRevokeJdxz(req));
    }

    @Operation(summary = "街道企业核定情况-退回")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("update/street/revoke")
    public ReData<String> updateStreetRevoke(@RequestBody @Validated UpdateStreetRevokeReq req) {
        districtEntCheckService.updateStreetRevoke(req);
        return ReData.success();
    }

    @Operation(summary = "街道企业核定情况-批次确定")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("update/street/batch/confirm")
    public ReData<String> updateStreetBatchConfirm(@RequestBody @Validated UpdateStreetBatchConfirmReq req) {
        districtEntCheckService.updateStreetBatchConfirm(req);
        return ReData.success();
    }

    @Operation(summary = "街道企业核定情况-批次重核")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("update/street/batch/review")
    public ReData<String> updateStreetBatchReview(@RequestBody @Validated UpdateStreetBatchConfirmReq req) {
        districtEntCheckService.updateStreetBatchReview(req);
        return ReData.success();
    }

    @Operation(summary = "街道企业核定情况-列表")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("page/street/issued/check")
    public ReData<Page<StreetIssuedCheckVo>> pageStreetIssuedCheck(@RequestBody @Validated PageStreetIssuedCheckReq req) {
        return ReData.success(districtEntCheckService.pageStreetIssuedCheck(req));
    }

    @Operation(summary = "区级企业核定批次-列表导出")
    @SaCheckPermission("districtEntCheck:export")
    @PostMapping("district/issued/check/export")
    public void exportDistrictIssuedCheck(@RequestBody @Validated PageDistrictEntIssuedBatchReq req, HttpServletResponse response) throws IOException {
        Page<DistrictIssuedBatchVo> page = districtEntCheckService.pageDistrictIssuedBatch(req);
        List<DistrictIssuedBatchVo> list = page.getRecords();
        List<DistrictIssuedBatchVo> result = new ArrayList<>();
        list.forEach(vo -> {
            result.add(vo);
            if (CollUtil.isNotEmpty(vo.getChildren())) {
                vo.getChildren().forEach(child -> {
                    child.setBatchNo("      " + child.getBatchNo());
                    result.add(child);
                });
            }
        });
        commonStaticExport(response, result, DistrictIssuedBatchVo.class, "区级企业核定批次", null);
    }

    @Operation(summary = "企业核定下发表-列表导出")
    @SaCheckPermission("districtEntCheck:export")
    @PostMapping("district/issued/batch/export")
    public void exportDistrictIssued(@RequestBody @Validated PageDistrictIssuedCheckReq req, HttpServletResponse response) throws IOException {
        Page<DistrictIssuedCheckVo> page = districtEntCheckService.pageDistrictIssuedCheck(req);
        commonStaticExport(response, page.getRecords(), DistrictIssuedCheckVo.class, "企业核定下发表", null);
    }

    @Operation(summary = "街道企业核定情况-列表导出")
    @SaCheckPermission("districtEntCheck:export")
    @PostMapping("street/issued/check/export")
    public void exportStreetIssuedCheck(@RequestBody @Validated PageStreetIssuedCheckReq req, HttpServletResponse response) throws IOException {
        Page<StreetIssuedCheckVo> page = districtEntCheckService.pageStreetIssuedCheck(req);
        commonStaticExport(response, page.getRecords(), StreetIssuedCheckVo.class, "街道企业核定情况", null);
    }

    @Operation(summary = "获取企业核定下发表批次状态")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("get/district/issued/check/status")
    public ReData<DistrictStatusVo> getDistrictIssuedCheckStatus(@RequestBody @Validated IdStringReq req) {
        return ReData.success(districtEntCheckService.getDistrictIssuedCheckStatus(req));
    }

    @Operation(summary = "获取街道企业核定情况批次状态")
    @SaCheckPermission("districtEntCheck")
    @PostMapping("get/street/issued/check/status")
    public ReData<DistrictStatusVo> getStreetIssuedCheckStatus(@RequestBody @Validated GetStreetIssuedCheckStatusReq req) {
        return ReData.success(districtEntCheckService.getStreetIssuedCheckStatus(req));
    }
}
