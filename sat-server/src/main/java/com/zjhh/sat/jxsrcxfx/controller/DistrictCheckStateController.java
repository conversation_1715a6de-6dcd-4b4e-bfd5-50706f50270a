package com.zjhh.sat.jxsrcxfx.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zjhh.comm.response.ReData;
import com.zjhh.cz.req.IdsStringReq;
import com.zjhh.db.comm.Page;
import com.zjhh.sat.jxsrcxfx.request.districtcheckstate.PageDistrictCheckStateReq;
import com.zjhh.sat.jxsrcxfx.request.districtcheckstate.UpdateCzfpReq;
import com.zjhh.sat.jxsrcxfx.service.DistrictCheckStateService;
import com.zjhh.sat.jxsrcxfx.vo.districtcheckstate.DistrictCheckStateVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2023/2/16 11:33
 */
@Tag(name = "嘉兴体制结算-区级核定情况")
@SaCheckLogin
@RequestMapping("district/check/state")
@RestController
public class DistrictCheckStateController extends BaseJxsrcxfxController {

    @Resource
    private DistrictCheckStateService districtCheckStateService;

    @Operation(summary = "列表")
    @SaCheckPermission("districtCheckState")
    @PostMapping("page")
    public ReData<Page<DistrictCheckStateVo>> pageDistrictCheckState(@RequestBody @Validated PageDistrictCheckStateReq req) {
        return ReData.success(districtCheckStateService.pageDistrictCheckState(req));
    }

    @Operation(summary = "财政分片")
    @SaCheckPermission("districtCheckState:fiscal")
    @PostMapping("update/czfp")
    public ReData<String> updateCzfp(@RequestBody @Validated UpdateCzfpReq req) {
        districtCheckStateService.updateCzfp(req);
        return ReData.success();
    }

    @Operation(summary = "确认")
    @SaCheckPermission("districtCheckState:confirm")
    @PostMapping("update/check")
    public ReData<String> updateCheck(@RequestBody @Validated IdsStringReq req) {
        districtCheckStateService.updateCheck(req);
        return ReData.success();
    }

    @Operation(summary = "作废")
    @SaCheckPermission("districtCheckState:cancel")
    @PostMapping("update/cancel")
    public ReData<String> updateCancel(@RequestBody @Validated IdsStringReq req) {
        districtCheckStateService.updateCancel(req);
        return ReData.success();
    }

    @Operation(summary = "列表导出")
    @SaCheckPermission("districtCheckState:export")
    @PostMapping("export")
    public void exportDistrictCheckState(@RequestBody @Validated PageDistrictCheckStateReq req, HttpServletResponse response) throws IOException {
        Page<DistrictCheckStateVo> page = districtCheckStateService.pageDistrictCheckState(req);
        commonStaticExport(response, page.getRecords(), DistrictCheckStateVo.class, "区级核定情况", null);
    }
}
