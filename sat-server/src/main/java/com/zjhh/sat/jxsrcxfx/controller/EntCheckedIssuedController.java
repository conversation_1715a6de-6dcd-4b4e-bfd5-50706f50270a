package com.zjhh.sat.jxsrcxfx.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.sat.jxsrcxfx.request.entcheckedissued.*;
import com.zjhh.sat.jxsrcxfx.service.EntCheckedIssuedService;
import com.zjhh.sat.jxsrcxfx.vo.entcheckedissued.EntCheckedIssuedVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2022/11/23 11:25
 */
@Tag(name = "嘉兴体制结算-企业核定下发")
@SaCheckLogin
@RequestMapping("ent/checked/issued")
@RestController
public class EntCheckedIssuedController extends BaseJxsrcxfxController {

    @Resource
    private EntCheckedIssuedService entCheckedIssuedService;

    @Operation(summary = "获取企业核定下发列表")
    @SaCheckPermission("enterCheckIssued")
    @PostMapping("page")
    public ReData<Page<EntCheckedIssuedVo>> pageEntCheckedIssued(@RequestBody @Validated PageEntCheckedIssuedReq req) {
        return ReData.success(entCheckedIssuedService.pageEntCheckedIssued(req));
    }

    @Operation(summary = "获取企业核修改为外来建筑定下发列表")
    @SaCheckPermission("enterCheckIssued:foreign")
    @PostMapping("update/wljz")
    public ReData<String> updateWljz(@RequestBody @Validated UpdateWljzReq req) {
        entCheckedIssuedService.updateWljz(req);
        return ReData.success();
    }

    @Operation(summary = "修改财政分片")
    @SaCheckPermission("enterCheckIssued:fiscal")
    @PostMapping("update/czfp")
    public ReData<String> updateCzfp(@RequestBody @Validated UpdateCzfqReq req) {
        entCheckedIssuedService.updateCzfp(req);
        return ReData.success();
    }

    @Operation(summary = "全部导出")
    @SaCheckPermission("enterCheckIssued:export")
    @PostMapping("export/all")
    public void exportAll(@RequestBody @Validated PageEntCheckedIssuedReq req, HttpServletResponse response) throws IOException {
        Page<EntCheckedIssuedVo> page = entCheckedIssuedService.pageEntCheckedIssued(req);
        commonStaticExport(response, page.getRecords(), EntCheckedIssuedVo.class, "企业核定下发", null);
    }

    @Operation(summary = "市级下发给区")
    @SaCheckPermission("enterCheckIssued:export")
    @PostMapping("update/issued")
    public ReData<String> updateIssued(@RequestBody @Validated UpdateIssuedReq req) {
        entCheckedIssuedService.updateIssued(req);
        return ReData.success();
    }

}
