package com.zjhh.sat.jxsrcxfx.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 税务本地登记纳税人信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_sat_bddj_nsrxx")
public class AdsSatBddjNsrxx implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 企业id
     */
    @TableId(type = IdType.INPUT)
    private String qyid;

    /**
     * 登记序号
     */
    private String djxh;

    /**
     * N-不是疑似外来建筑 Y-疑似外来建筑
     */
    private String yswljz;


}
