server:
    servlet:
        context-path: /auth_server
jasypt:
    encryptor:
        algorithm: PBEWithMD5AndDES
        iv-generator-classname: org.jasypt.iv.NoIvGenerator
spring:
    application:
        name: auth-server
    cloud:
        nacos:
            server-addr: 192.168.1.206:31048
            config:
                namespace: temp
                file-extension: yaml
                prefix: auth-server
                group: DEFAULT_GROUP
            discovery:
                namespace: ${spring.cloud.nacos.config.namespace}
                group: DEFAULT_GROUP
                enabled: true
    config:
        import: optional:nacos:auth-server

platform:
    version: V1.0.1
