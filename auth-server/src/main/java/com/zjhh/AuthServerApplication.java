package com.zjhh;

import cn.dev33.satoken.SaManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

/**
 * <AUTHOR>
 * @since 2020/6/12 14:00
 */
@Slf4j
@EnableDiscoveryClient
@SpringBootApplication
public class AuthServerApplication {

    public static void main(String[] args) {
        System.setProperty("server.port", "8020");
        ConfigurableApplicationContext application = SpringApplication.run(AuthServerApplication.class, args);
        Environment env = application.getEnvironment();
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path");
        log.info("\n----------------------------------------------------------\n\t" +
                "Application Zjhh-platform is running! Access URLs:\n\t" +
                "Local: \t\thttp://localhost:" + port + path + "/\n\t" +
                "External: \thttp://localhost:" + port + path + "/\n\t" +
                "Swagger-ui: \thttp://localhost:" + port + path + "/doc.html\n\t" +
                "sa-token配置如下：" + SaManager.getConfig() +
                "----------------------------------------------------------");
    }
} 