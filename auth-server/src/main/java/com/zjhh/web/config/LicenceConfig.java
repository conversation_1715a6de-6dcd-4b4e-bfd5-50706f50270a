package com.zjhh.web.config;

import com.zjhh.user.service.LicenceService;
import com.zjhh.user.service.impl.CustomerLicenceServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/11/23 14:10
 */
@Configuration
public class LicenceConfig {

    @Bean
    public LicenceService licenceService() {
        return new CustomerLicenceServiceImpl();
    }
}
