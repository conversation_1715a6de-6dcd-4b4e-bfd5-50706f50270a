package com.zjhh.web.controller.login;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.core.util.StrUtil;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.response.ReData;
import com.zjhh.system.annotation.ApiLog;
import com.zjhh.system.enume.LogApiTypeEnum;
import com.zjhh.user.request.FOALoginReq;
import com.zjhh.user.request.LoginReq;
import com.zjhh.user.request.QrCodeLoginReq;
import com.zjhh.user.request.StThirdLoginReq;
import com.zjhh.user.service.LoginService;
import com.zjhh.user.vo.CaptchaVo;
import com.zjhh.user.vo.InitConfigVo;
import com.zjhh.user.vo.LoginVo;
import com.zjhh.user.vo.ZwddLoginVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2020/6/11 17:08
 */
@Slf4j
@Tag(name = "登录")
@RestController
public class LoginController extends BaseController {

    @Resource
    private LoginService loginService;

    @Operation(summary = "登录")
    @ApiLog(value = "登录", type = LogApiTypeEnum.LOGIN)
    @PostMapping("login")
    public ReData<LoginVo> login(@RequestBody @Validated LoginReq req) {
        return ReData.success(loginService.login(req));
    }

    @Operation(summary = "扫码登录")
    @PostMapping("qr_code/login")
    public ReData<ZwddLoginVo> qrCodelogin(@RequestBody @Validated QrCodeLoginReq req) {
        return ReData.success(loginService.getEmployeeCodeByQrCode(req.getCode()));
    }

    @Operation(summary = "免登录")
    @PostMapping("auth_code/login")
    public ReData<ZwddLoginVo> authCodeLogin(@RequestBody @Validated QrCodeLoginReq req) {
        return ReData.success(loginService.getEmployeeCodeByAuthCode(req.getCode()));
    }


    @SaCheckLogin
    @Operation(summary = "退出登录")
    @ApiLog(value = "退出登录", type = LogApiTypeEnum.LOGOUT)
    @PostMapping("logout")
    public ReData<String> logout() {
        loginService.logout();
        return ReData.success("退出登录成功！");
    }

    @Operation(summary = "获取图形验证码")
    @PostMapping("get/captcha")
    public ReData<CaptchaVo> createCaptcha() {
        return ReData.success(loginService.createCaptcha());
    }

    @Operation(summary = "获取初始化配置")
    @RequestMapping("get/init/config")
    public ReData<InitConfigVo> getInitConfig() {
        return ReData.success(loginService.getInitConfig());
    }

    @Operation(summary = "第三方单点登录")
    @PostMapping("third/login")
    public ReData<LoginVo> thirdLogin(@RequestBody @Validated StThirdLoginReq req) {
        return ReData.success(loginService.thirdLogin(req));
    }

    @Operation(summary = "获取在线人数")
    @PostMapping("count/online/user")
    @SaCheckLogin
    public ReData<Integer> countOnlineUser() {
        return ReData.success(loginService.countOnlineUser());
    }

    @Operation(summary = "省厅OA单点登录")
    @PostMapping("foalogin")
    public ReData<LoginVo> foaLogin(@RequestBody FOALoginReq req) {
        if (StrUtil.isBlank(req.getAppCode()) || StrUtil.isBlank(req.getPassport())) {
            log.error("登录信息缺失，登录的参数为：{}", req);
            throw new BizException("登录失败，请联系管理员！");
        }
        return ReData.success(loginService.foaLogin(req));
    }
}
