package com.zjhh.user.service.impl;

import com.zjhh.comm.vo.LicenceDataVo;
import com.zjhh.comm.vo.LicenceValidVo;
import com.zjhh.user.service.LicenceService;

import jakarta.annotation.Resource;
import java.io.File;

/**
 * <AUTHOR>
 * @since 2023/11/23 14:06
 */
public class CustomerLicenceServiceImpl extends LicenceServiceImpl implements LicenceService {

    @Resource
    private UserSession userSession;

    @Override
    public LicenceValidVo refreshLicence(File file, Boolean flag) {
        LicenceValidVo validVo = new LicenceValidVo();
        validVo.setValid(true);
        LicenceDataVo licenceDataVo = new LicenceDataVo();
        licenceDataVo.setExpireDate("2999-01-01");
        validVo.setLicenceData(licenceDataVo);
        userSession.setLicence(validVo);
        return validVo;
    }
}
